---
// <PERSON>N Optimized Image Component
// Specifically designed for <PERSON> CDN with advanced optimization features

export interface Props {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  sizes?: string;
  loading?: 'lazy' | 'eager';
  fetchpriority?: 'high' | 'low' | 'auto';
  class?: string;
  style?: string;
  // Bunny CDN specific optimization
  quality?: number;
  format?: 'webp' | 'avif' | 'png' | 'jpg' | 'auto';
  blur?: number;
  brightness?: number;
  contrast?: number;
  gamma?: number;
  saturation?: number;
  sharpen?: number;
  crop?: 'center' | 'top' | 'bottom' | 'left' | 'right' | 'smart';
  fit?: 'contain' | 'cover' | 'fill' | 'inside' | 'outside';
  // Responsive breakpoints
  breakpoints?: {
    mobile?: number;
    tablet?: number;
    desktop?: number;
    wide?: number;
  };
  // Fallback and error handling
  fallbackSrc?: string;
  placeholder?: string;
}

const {
  src,
  alt,
  width = 800,
  height = 600,
  sizes = '(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw',
  loading = 'lazy',
  fetchpriority = 'auto',
  class: className = '',
  style = '',
  quality = 85,
  format = 'auto',
  blur,
  brightness,
  contrast,
  gamma,
  saturation,
  sharpen,
  crop = 'smart',
  fit = 'cover',
  breakpoints = {
    mobile: 480,
    tablet: 768,
    desktop: 1200,
    wide: 1600
  },
  fallbackSrc,
  placeholder
} = Astro.props;

// Build Bunny CDN optimization parameters
function buildBunnyParams(width?: number, height?: number, customParams: Record<string, any> = {}) {
  const params = new URLSearchParams();
  
  // Core parameters
  if (width) params.set('width', width.toString());
  if (height) params.set('height', height.toString());
  params.set('quality', quality.toString());
  if (format !== 'auto') params.set('format', format);
  params.set('fit', fit);
  if (crop !== 'smart') params.set('crop', crop);
  
  // Enhancement parameters
  if (blur !== undefined) params.set('blur', blur.toString());
  if (brightness !== undefined) params.set('brightness', brightness.toString());
  if (contrast !== undefined) params.set('contrast', contrast.toString());
  if (gamma !== undefined) params.set('gamma', gamma.toString());
  if (saturation !== undefined) params.set('saturation', saturation.toString());
  if (sharpen !== undefined) params.set('sharpen', sharpen.toString());
  
  // Custom parameters
  Object.entries(customParams).forEach(([key, value]) => {
    if (value !== undefined) params.set(key, value.toString());
  });
  
  return params.toString();
}

// Generate responsive image URLs
function generateResponsiveUrls(baseSrc: string) {
  const baseUrl = baseSrc.split('?')[0]; // Remove existing params
  
  return {
    mobile: `${baseUrl}?${buildBunnyParams(breakpoints.mobile, Math.round((breakpoints.mobile! / width) * height))}`,
    tablet: `${baseUrl}?${buildBunnyParams(breakpoints.tablet, Math.round((breakpoints.tablet! / width) * height))}`,
    desktop: `${baseUrl}?${buildBunnyParams(breakpoints.desktop, Math.round((breakpoints.desktop! / width) * height))}`,
    wide: `${baseUrl}?${buildBunnyParams(breakpoints.wide, Math.round((breakpoints.wide! / width) * height))}`,
    default: `${baseUrl}?${buildBunnyParams(width, height)}`
  };
}

// Generate srcset string
function generateSrcSet(baseSrc: string) {
  const urls = generateResponsiveUrls(baseSrc);
  return `${urls.mobile} ${breakpoints.mobile}w, ${urls.tablet} ${breakpoints.tablet}w, ${urls.desktop} ${breakpoints.desktop}w, ${urls.wide} ${breakpoints.wide}w`;
}

const responsiveUrls = generateResponsiveUrls(src);
const srcSet = generateSrcSet(src);

// Generate low-quality placeholder for better UX
const placeholderUrl = placeholder || `${src.split('?')[0]}?${buildBunnyParams(50, Math.round((50 / width) * height), { blur: 10, quality: 30 })}`;
---

<div class="bunny-image-container" style={style}>
  {placeholder && (
    <img
      src={placeholderUrl}
      alt=""
      class="bunny-image-placeholder"
      aria-hidden="true"
      loading="eager"
      decoding="async"
    />
  )}
  
  <img
    src={responsiveUrls.default}
    srcset={srcSet}
    sizes={sizes}
    alt={alt}
    width={width}
    height={height}
    loading={loading}
    fetchpriority={fetchpriority}
    class={`bunny-image ${className}`}
    decoding="async"
    onload="this.parentElement.classList.add('loaded')"
    onerror={fallbackSrc ? `this.src='${fallbackSrc}'; this.parentElement.classList.add('error')` : "this.parentElement.classList.add('error')"}
  />
</div>

<style>
  .bunny-image-container {
    position: relative;
    display: block;
    overflow: hidden;
    background: var(--border-light);
  }
  
  .bunny-image {
    width: 100%;
    height: auto;
    display: block;
    transition: opacity 0.3s ease;
  }
  
  .bunny-image-placeholder {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    filter: blur(10px);
    transform: scale(1.1);
    transition: opacity 0.3s ease;
    z-index: 1;
  }
  
  .bunny-image-container:not(.loaded) .bunny-image {
    opacity: 0;
  }
  
  .bunny-image-container.loaded .bunny-image-placeholder {
    opacity: 0;
    pointer-events: none;
  }
  
  .bunny-image-container.loaded .bunny-image {
    opacity: 1;
  }
  
  /* Error state */
  .bunny-image-container.error {
    background: var(--border-light);
    border: 1px solid var(--border);
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    color: var(--text-secondary);
  }
  
  .bunny-image-container.error::before {
    content: "Image failed to load";
    font-size: 0.875rem;
    text-align: center;
  }
  
  .bunny-image-container.error .bunny-image,
  .bunny-image-container.error .bunny-image-placeholder {
    display: none;
  }
  
  /* Aspect ratio preservation */
  .bunny-image-container {
    aspect-ratio: var(--aspect-ratio, auto);
  }
  
  /* Loading animation */
  .bunny-image-container:not(.loaded):not(.error) {
    background: linear-gradient(90deg, var(--border-light) 25%, var(--border) 50%, var(--border-light) 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
  }
  
  @keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
  }
  
  /* Responsive adjustments */
  @media (max-width: 640px) {
    .bunny-image-container {
      border-radius: var(--radius);
    }
  }
</style>

<script>
  // Enhanced error handling and loading states
  document.addEventListener('DOMContentLoaded', () => {
    const imageContainers = document.querySelectorAll('.bunny-image-container');
    
    imageContainers.forEach(container => {
      const img = container.querySelector('.bunny-image') as HTMLImageElement;
      const placeholder = container.querySelector('.bunny-image-placeholder') as HTMLImageElement;
      
      if (img) {
        // Set aspect ratio CSS custom property
        const aspectRatio = img.width / img.height;
        (container as HTMLElement).style.setProperty('--aspect-ratio', aspectRatio.toString());
        
        // Handle successful load
        img.addEventListener('load', () => {
          container.classList.add('loaded');
        });
        
        // Handle error
        img.addEventListener('error', () => {
          container.classList.add('error');
        });
        
        // If image is already loaded (cached)
        if (img.complete && img.naturalHeight !== 0) {
          container.classList.add('loaded');
        }
      }
    });
  });
</script>

<!-- 
Usage Examples:

1. Basic product image:
<BunnyCDNImage 
  src="https://cdn.cheersmarketplace.com/products/shirt.jpg"
  alt="Organic Cotton T-Shirt"
  width={800}
  height={600}
  loading="lazy"
/>

2. Hero image with enhancements:
<BunnyCDNImage 
  src="https://cdn.cheersmarketplace.com/hero.jpg"
  alt="Hero banner"
  width={1200}
  height={600}
  loading="eager"
  fetchpriority="high"
  quality={90}
  sharpen={1}
  contrast={1.1}
  placeholder="true"
/>

3. Product gallery with custom breakpoints:
<BunnyCDNImage 
  src="https://cdn.cheersmarketplace.com/products/watch.jpg"
  alt="Minimalist Wall Clock"
  width={600}
  height={600}
  crop="center"
  fit="cover"
  breakpoints={{
    mobile: 300,
    tablet: 500,
    desktop: 800,
    wide: 1000
  }}
  fallbackSrc="/images/product-placeholder.jpg"
/>
-->
