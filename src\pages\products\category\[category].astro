---
export const prerender = true;

import Layout from '../../../layouts/Layout.astro';
import Header from '../../../components/Header.astro';
import Footer from '../../../components/Footer.astro';
import OptimizedProductsList from '../../../components/OptimizedProductsList.astro';
import BreadcrumbWrapper from '../../../components/navigation/BreadcrumbWrapper.astro';
import { generateSlug } from '../../../lib/products';
import { generateProductsListingDescription } from '../../../utils/metaDescriptions.ts';

// Import products data directly for maximum performance
import products from '../../../data/products.json';
import createdCategories from '../../../data/categories.json';

export async function getStaticPaths() {
  // Get categories from products
  const productCategories = [...new Set(products.map(p => p.category))].filter(Boolean);

  // Combine with manually created categories
  const categories = [...new Set([...productCategories, ...createdCategories])].sort();
  
  return categories.map((category) => {
    const categoryProducts = products.filter(p => p.category === category);
    const categorySlug = generateSlug(category);
    
    return {
      params: { category: categorySlug },
      props: { 
        category,
        categorySlug,
        categoryProducts,
        totalProducts: categoryProducts.length
      },
    };
  });
}

const { category, categorySlug, categoryProducts, totalProducts } = Astro.props;

// Generate price range for this category
const prices = categoryProducts.map(p => Number(p.price || 0));
const priceRange = {
  min: Math.min(...prices),
  max: Math.max(...prices)
};

// Generate SEO-optimized meta description
const metaDescription = generateProductsListingDescription(category, undefined, totalProducts);
const pageTitle = `${category} - Cheers Marketplace`;

// Generate structured data for the category page
const structuredData = {
  "@context": "https://schema.org",
  "@type": "CollectionPage",
  "name": `${category} Products`,
  "description": metaDescription,
  "url": `https://www.cheersmarketplace.com/products/category/${categorySlug}/`,
  "mainEntity": {
    "@type": "ItemList",
    "numberOfItems": totalProducts,
    "itemListElement": categoryProducts.slice(0, 10).map((product, index) => ({
      "@type": "Product",
      "position": index + 1,
      "name": product.name,
      "description": product.description,
      "image": product.images?.[0] || '',
      "offers": {
        "@type": "Offer",
        "price": product.price,
        "priceCurrency": "USD",
        "availability": "https://schema.org/InStock",
        "itemCondition": "https://schema.org/UsedCondition"
      },
      "url": `https://www.cheersmarketplace.com/products/${generateSlug(product.name)}/`
    }))
  }
};

// Generate breadcrumb structured data
const breadcrumbData = {
  "@context": "https://schema.org",
  "@type": "BreadcrumbList",
  "itemListElement": [
    {
      "@type": "ListItem",
      "position": 1,
      "name": "Home",
      "item": "https://www.cheersmarketplace.com/"
    },
    {
      "@type": "ListItem",
      "position": 2,
      "name": "Products",
      "item": "https://www.cheersmarketplace.com/products/"
    },
    {
      "@type": "ListItem",
      "position": 3,
      "name": category,
      "item": `https://www.cheersmarketplace.com/products/category/${categorySlug}/`
    }
  ]
};
---

<Layout
  title={pageTitle}
  description={metaDescription}
  canonical={`https://www.cheersmarketplace.com/products/category/${categorySlug}/`}
>
  <Fragment slot="head">
    <!-- Structured Data -->
    <script type="application/ld+json" set:html={JSON.stringify(structuredData)} />
    <script type="application/ld+json" set:html={JSON.stringify(breadcrumbData)} />
    
    <!-- Open Graph -->
    <meta property="og:title" content={pageTitle} />
    <meta property="og:description" content={metaDescription} />
    <meta property="og:type" content="website" />
    <meta property="og:url" content={`https://www.cheersmarketplace.com/products/category/${categorySlug}/`} />
    
    <!-- Twitter Card -->
    <meta name="twitter:card" content="summary" />
    <meta name="twitter:title" content={pageTitle} />
    <meta name="twitter:description" content={metaDescription} />
    
    <!-- Category-specific meta -->
    <meta name="product-category" content={category} />
    <meta name="product-count" content={totalProducts.toString()} />
    
    <!-- Preload critical resources -->
    <link rel="preload" href="/scripts/products-filter.js" as="script" />
  </Fragment>

  <Header />

  <!-- Breadcrumb Navigation -->
  <BreadcrumbWrapper 
    pageTitle={`${category} Products`}
    showBreadcrumbs={true}
  />

  <main class="products-main">
    <div class="container">
      <!-- Category Header -->
      <div class="category-header">
        <div class="category-title-section">
          <h1 class="category-title">{category}</h1>
          <p class="category-description">
            Discover our collection of {totalProducts} {category.toLowerCase()} items. 
            All products are carefully inspected and priced to offer great value.
          </p>
        </div>
        
        <div class="category-stats">
          <div class="stat-item">
            <span class="stat-number">{totalProducts}</span>
            <span class="stat-label">Products</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">${priceRange.min}</span>
            <span class="stat-label">Starting at</span>
          </div>
        </div>
      </div>

      <!-- Products Section -->
      <div class="products-section">
        <div class="products-header">
          <h2 class="section-title">All {category} Products</h2>
          
          <div class="header-controls">
            <select id="sort-by" class="control-select" aria-label="Sort products by different criteria">
              <option value="newest">Newest First</option>
              <option value="name">Name A-Z</option>
              <option value="price-low">Price: Low to High</option>
              <option value="price-high">Price: High to Low</option>
            </select>
            
            <div class="search-container">
              <input 
                type="search" 
                id="search-input" 
                class="search-input" 
                placeholder={`Search ${category.toLowerCase()}...`}
                aria-label={`Search ${category} products`}
              />
              <svg class="search-icon" width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
              </svg>
            </div>
          </div>
        </div>

        <!-- Products Grid -->
        <OptimizedProductsList 
          products={categoryProducts}
          showFilters={false}
          initialCategory={category}
        />
      </div>
    </div>
  </main>

  <Footer />
</Layout>

<!-- Load pagination script directly -->
<script src="/scripts/products-pagination.js" is:inline></script>

<!-- Load filtering and sorting JavaScript -->
<script src="/scripts/products-filter.js" is:inline></script>
<script is:inline>
  // Initialize category page functionality
  document.addEventListener('DOMContentLoaded', () => {
    // Initialize the products filter for category page
    if (window.ProductsFilter) {
      window.ProductsFilter.init();
      
      // Hide category filter since we're already on a category page
      const categoryFilter = document.getElementById('category-filter');
      if (categoryFilter) {
        categoryFilter.style.display = 'none';
      }
    }
  });
</script>

<style>
  .category-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 2rem;
    margin-bottom: 3rem;
    padding: 2rem 0;
    border-bottom: 1px solid var(--border-light);
  }

  .category-title-section {
    flex: 1;
  }

  .category-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text);
    margin: 0 0 1rem 0;
    line-height: 1.2;
  }

  .category-description {
    font-size: 1.125rem;
    color: var(--text-secondary);
    line-height: 1.6;
    margin: 0;
    max-width: 600px;
  }

  .category-stats {
    display: flex;
    gap: 2rem;
    flex-shrink: 0;
  }

  .stat-item {
    text-align: center;
    padding: 1rem;
    background: var(--light-background);
    border-radius: var(--radius);
    border: 1px solid var(--border-light);
    min-width: 100px;
  }

  .stat-number {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary);
    margin-bottom: 0.25rem;
  }

  .stat-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .products-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
  }

  .section-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text);
    margin: 0;
  }

  .header-controls {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
  }

  .control-select {
    padding: 0.5rem 1rem;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    background: white;
    font-size: 0.875rem;
    color: var(--text);
    cursor: pointer;
    transition: border-color 0.2s ease;
  }

  .control-select:hover,
  .control-select:focus {
    border-color: var(--primary);
    outline: none;
  }

  .search-container {
    position: relative;
    display: flex;
    align-items: center;
  }

  .search-input {
    padding: 0.5rem 2.5rem 0.5rem 1rem;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    font-size: 0.875rem;
    width: 250px;
    transition: border-color 0.2s ease;
  }

  .search-input:focus {
    border-color: var(--primary);
    outline: none;
  }

  .search-icon {
    position: absolute;
    right: 0.75rem;
    color: var(--text-secondary);
    pointer-events: none;
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .category-header {
      flex-direction: column;
      gap: 1.5rem;
      text-align: center;
    }

    .category-title {
      font-size: 2rem;
    }

    .category-stats {
      justify-content: center;
      gap: 1rem;
    }

    .stat-item {
      min-width: 80px;
      padding: 0.75rem;
    }

    .products-header {
      flex-direction: column;
      align-items: stretch;
      gap: 1rem;
    }

    .header-controls {
      justify-content: space-between;
    }

    .search-input {
      width: 200px;
    }
  }

  @media (max-width: 480px) {
    .category-stats {
      flex-direction: column;
      gap: 0.75rem;
    }

    .header-controls {
      flex-direction: column;
      gap: 0.75rem;
    }

    .search-input {
      width: 100%;
    }
  }
</style>
