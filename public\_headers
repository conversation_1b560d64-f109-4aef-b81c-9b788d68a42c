# Enhanced Caching Rules for Cloudflare Pages
# Optimized for performance, security, and SEO

# === IMMUTABLE ASSETS (1 year cache) ===
# Framework assets with hashes - these never change
/_astro/*
  Cache-Control: public, max-age=31536000, immutable
  X-Content-Type-Options: nosniff
  Cross-Origin-Embedder-Policy: require-corp
  Cross-Origin-Opener-Policy: same-origin

# Hashed CSS and JS files from our build
/assets/css/*
  Cache-Control: public, max-age=31536000, immutable
  X-Content-Type-Options: nosniff
  Content-Type: text/css

/assets/js/*
  Cache-Control: public, max-age=31536000, immutable
  X-Content-Type-Options: nosniff
  Content-Type: application/javascript

# Optimized JavaScript bundles (1 year cache)
/scripts/*.js
  Cache-Control: public, max-age=31536000, immutable
  X-Content-Type-Options: nosniff
  Content-Type: application/javascript

# === STATIC ASSETS (1 week cache) ===
# Images, fonts, and other static content
/assets/*
  Cache-Control: public, max-age=604800, stale-while-revalidate=86400
  X-Content-Type-Options: nosniff
  Vary: Accept-Encoding

# Favicon and icons (1 week cache)
/favicon.svg
  Cache-Control: public, max-age=604800
  X-Content-Type-Options: nosniff
  Content-Type: image/svg+xml

/images/*
  Cache-Control: public, max-age=604800, stale-while-revalidate=86400
  X-Content-Type-Options: nosniff
  Vary: Accept-Encoding

# === FONTS (1 month cache) ===
# Web fonts should be cached longer
*.woff2
  Cache-Control: public, max-age=2592000, immutable
  X-Content-Type-Options: nosniff
  Content-Type: font/woff2
  Cross-Origin-Resource-Policy: cross-origin

*.woff
  Cache-Control: public, max-age=2592000, immutable
  X-Content-Type-Options: nosniff
  Content-Type: font/woff
  Cross-Origin-Resource-Policy: cross-origin

# === API ENDPOINTS ===
# Product data API - cache for 5 minutes with stale-while-revalidate
/api/products.json
  Cache-Control: public, max-age=300, s-maxage=300, stale-while-revalidate=600
  Cache-Tag: products,api,product-list
  Content-Type: application/json
  Vary: Accept-Encoding

# Static JSON data files
/data/*
  Cache-Control: public, max-age=300, s-maxage=300, stale-while-revalidate=600
  Cache-Tag: data
  Content-Type: application/json
  X-Content-Type-Options: nosniff

# === SPECIAL FILES ===
# Sitemap and robots - cache for 1 day
/sitemap.xml
  Cache-Control: public, max-age=86400, stale-while-revalidate=3600
  Content-Type: application/xml
  Cache-Tag: sitemap,seo

/robots.txt
  Cache-Control: public, max-age=86400
  Content-Type: text/plain
  Cache-Tag: robots,seo

# === HTML PAGES ===
# Homepage - cache for 1 hour with aggressive revalidation
/
  Cache-Control: public, max-age=3600, stale-while-revalidate=1800
  Cache-Tag: homepage,html,pages
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(), microphone=(), geolocation=()
  Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.snipcart.com https://www.googletagmanager.com https://app.snipcart.com; style-src 'self' 'unsafe-inline' https://cdn.snipcart.com https://fonts.googleapis.com https://fonts.bunny.net; img-src 'self' data: https: blob:; font-src 'self' data: https://fonts.gstatic.com https://fonts.bunny.net https://cdn.snipcart.com; connect-src 'self' https: https://app.snipcart.com https://api.snipcart.com; frame-src 'self' https:; worker-src 'self'

# Product pages - cache for 2 hours (less frequent updates)
/products/*
  Cache-Control: public, max-age=7200, stale-while-revalidate=3600
  Cache-Tag: products,product-detail,html,pages
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(), microphone=(), geolocation=()

# Static pages - cache for 4 hours
/about
/faq
/terms
/privacy
  Cache-Control: public, max-age=14400, stale-while-revalidate=7200
  Cache-Tag: static,html,pages
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  Referrer-Policy: strict-origin-when-cross-origin

# === ADMIN AND SENSITIVE AREAS ===
# Admin panel - no cache, security headers
/admin
/admin/*
  Cache-Control: no-cache, no-store, must-revalidate, private
  X-Robots-Tag: noindex, nofollow
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  Referrer-Policy: strict-origin
  Permissions-Policy: camera=(), microphone=(), geolocation=(), payment=()

# Debug pages - no cache in production
/debug-sync
  Cache-Control: no-cache, no-store, must-revalidate
  X-Robots-Tag: noindex, nofollow

# === DEFAULT HTML PAGES ===
# All other HTML pages - moderate caching
/*
  Cache-Control: public, max-age=3600, stale-while-revalidate=1800
  Cache-Tag: html,pages
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(), microphone=(), geolocation=()
