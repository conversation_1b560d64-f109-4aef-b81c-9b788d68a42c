---
/**
 * Performance Monitor Component
 * Tracks Core Web Vitals and performance metrics to prevent regressions
 */

export interface Props {
  enableCoreWebVitals?: boolean;
  enableResourceTiming?: boolean;
  enableUserTiming?: boolean;
  enableErrorTracking?: boolean;
  reportingEndpoint?: string;
  sampleRate?: number;
}

const {
  enableCoreWebVitals = true,
  enableResourceTiming = true,
  enableUserTiming = true,
  enableErrorTracking = true,
  reportingEndpoint = '/api/performance',
  sampleRate = 0.1 // 10% sampling rate
} = Astro.props;
---

<!-- Performance Monitoring Script -->
<script is:inline defer define:vars={{
  enableCoreWebVitals,
  enableResourceTiming,
  enableUserTiming,
  enableErrorTracking,
  reportingEndpoint,
  sampleRate
}}>
  (function() {
    'use strict';
    
    // Performance Monitor
    const PerformanceMonitor = {
      metrics: {
        lcp: null,
        fid: null,
        cls: null,
        fcp: null,
        ttfb: null,
        inp: null
      },
      
      resourceMetrics: [],
      userMetrics: [],
      errors: [],
      
      // Initialize performance monitoring
      init: function() {
        // Only monitor for a sample of users
        if (Math.random() > sampleRate) return;
        
        if (enableCoreWebVitals) {
          this.measureCoreWebVitals();
        }
        
        if (enableResourceTiming) {
          this.measureResourceTiming();
        }
        
        if (enableUserTiming) {
          this.measureUserTiming();
        }
        
        if (enableErrorTracking) {
          this.trackErrors();
        }
        
        // Report metrics when page is about to unload
        this.setupReporting();
      },
      
      // Measure Core Web Vitals
      measureCoreWebVitals: function() {
        // Largest Contentful Paint (LCP)
        if ('PerformanceObserver' in window) {
          try {
            const lcpObserver = new PerformanceObserver((list) => {
              const entries = list.getEntries();
              const lastEntry = entries[entries.length - 1];
              this.metrics.lcp = lastEntry.startTime;
              console.log('LCP:', lastEntry.startTime);
            });
            lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
          } catch (e) {
            console.warn('LCP measurement not supported');
          }
          
          // First Input Delay (FID)
          try {
            const fidObserver = new PerformanceObserver((list) => {
              const entries = list.getEntries();
              entries.forEach(entry => {
                this.metrics.fid = entry.processingStart - entry.startTime;
                console.log('FID:', this.metrics.fid);
              });
            });
            fidObserver.observe({ entryTypes: ['first-input'] });
          } catch (e) {
            console.warn('FID measurement not supported');
          }
          
          // Cumulative Layout Shift (CLS)
          try {
            // Check if layout-shift is supported
            if (PerformanceObserver.supportedEntryTypes && PerformanceObserver.supportedEntryTypes.includes('layout-shift')) {
              let clsValue = 0;
              const clsObserver = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                entries.forEach(entry => {
                  if (!entry.hadRecentInput) {
                    clsValue += entry.value;
                  }
                });
                this.metrics.cls = clsValue;
                console.log('CLS:', clsValue);
              });
              clsObserver.observe({ entryTypes: ['layout-shift'] });
            } else {
              console.warn('layout-shift entryType not supported in this browser');
            }
          } catch (e) {
            console.warn('CLS measurement not supported');
          }
          
          // First Contentful Paint (FCP)
          try {
            const fcpObserver = new PerformanceObserver((list) => {
              const entries = list.getEntries();
              entries.forEach(entry => {
                if (entry.name === 'first-contentful-paint') {
                  this.metrics.fcp = entry.startTime;
                  console.log('FCP:', entry.startTime);
                }
              });
            });
            fcpObserver.observe({ entryTypes: ['paint'] });
          } catch (e) {
            console.warn('FCP measurement not supported');
          }
          
          // Interaction to Next Paint (INP) - newer metric
          try {
            // Check if event entryType is supported
            if (PerformanceObserver.supportedEntryTypes && PerformanceObserver.supportedEntryTypes.includes('event')) {
              const inpObserver = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                entries.forEach(entry => {
                  const inp = entry.processingStart - entry.startTime + entry.duration;
                  if (!this.metrics.inp || inp > this.metrics.inp) {
                    this.metrics.inp = inp;
                    console.log('INP:', inp);
                  }
                });
              });
              inpObserver.observe({ entryTypes: ['event'] });
            } else {
              console.warn('event entryType not supported in this browser');
            }
          } catch (e) {
            console.warn('INP measurement not supported');
          }
        }
        
        // Time to First Byte (TTFB)
        if (performance.timing) {
          this.metrics.ttfb = performance.timing.responseStart - performance.timing.navigationStart;
        } else if (performance.getEntriesByType) {
          const navigationEntries = performance.getEntriesByType('navigation');
          if (navigationEntries.length > 0) {
            this.metrics.ttfb = navigationEntries[0].responseStart;
          }
        }
      },
      
      // Measure resource timing
      measureResourceTiming: function() {
        if ('PerformanceObserver' in window) {
          const resourceObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            entries.forEach(entry => {
              const resourceMetric = {
                name: entry.name,
                type: this.getResourceType(entry.name),
                duration: entry.duration,
                transferSize: entry.transferSize || 0,
                decodedBodySize: entry.decodedBodySize || 0,
                startTime: entry.startTime,
                isCached: entry.transferSize === 0 && entry.decodedBodySize > 0
              };
              
              this.resourceMetrics.push(resourceMetric);
              
              // Log slow resources
              if (entry.duration > 1000) {
                console.warn('Slow resource detected:', resourceMetric);
              }
            });
          });
          
          resourceObserver.observe({ entryTypes: ['resource'] });
        }
      },
      
      // Measure user timing
      measureUserTiming: function() {
        if ('PerformanceObserver' in window) {
          const userTimingObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            entries.forEach(entry => {
              this.userMetrics.push({
                name: entry.name,
                startTime: entry.startTime,
                duration: entry.duration || 0,
                type: entry.entryType
              });
            });
          });
          
          userTimingObserver.observe({ entryTypes: ['measure', 'mark'] });
        }
      },
      
      // Track JavaScript errors
      trackErrors: function() {
        window.addEventListener('error', (event) => {
          this.errors.push({
            message: event.message,
            filename: event.filename,
            lineno: event.lineno,
            colno: event.colno,
            timestamp: Date.now(),
            type: 'javascript'
          });
        });
        
        window.addEventListener('unhandledrejection', (event) => {
          this.errors.push({
            message: event.reason?.message || 'Unhandled Promise Rejection',
            timestamp: Date.now(),
            type: 'promise'
          });
        });
      },
      
      // Get resource type from URL
      getResourceType: function(url) {
        if (url.includes('.css')) return 'css';
        if (url.includes('.js')) return 'javascript';
        if (url.match(/\.(jpg|jpeg|png|gif|webp|avif|svg)$/i)) return 'image';
        if (url.match(/\.(woff|woff2|ttf|otf)$/i)) return 'font';
        if (url.includes('api/') || url.includes('.json')) return 'api';
        return 'other';
      },
      
      // Setup reporting
      setupReporting: function() {
        // Report on page visibility change (when user switches tabs)
        document.addEventListener('visibilitychange', () => {
          if (document.visibilityState === 'hidden') {
            this.reportMetrics();
          }
        });
        
        // Report on page unload
        window.addEventListener('beforeunload', () => {
          this.reportMetrics();
        });
        
        // Report periodically for long sessions
        setInterval(() => {
          this.reportMetrics();
        }, 30000); // Every 30 seconds
      },
      
      // Report metrics to server
      reportMetrics: function() {
        const report = {
          timestamp: Date.now(),
          url: window.location.href,
          userAgent: navigator.userAgent,
          connection: this.getConnectionInfo(),
          coreWebVitals: this.metrics,
          resourceMetrics: this.getResourceSummary(),
          userMetrics: this.userMetrics,
          errors: this.errors,
          performance: this.getPerformanceSummary()
        };
        
        // Use sendBeacon for reliable reporting
        if (navigator.sendBeacon && reportingEndpoint) {
          navigator.sendBeacon(reportingEndpoint, JSON.stringify(report));
        } else {
          // Fallback to fetch with keepalive
          fetch(reportingEndpoint, {
            method: 'POST',
            body: JSON.stringify(report),
            headers: {
              'Content-Type': 'application/json'
            },
            keepalive: true
          }).catch(err => {
            console.warn('Failed to report performance metrics:', err);
          });
        }
        
        // Log to console for debugging
        console.log('Performance Report:', report);
      },
      
      // Get connection information
      getConnectionInfo: function() {
        if ('connection' in navigator) {
          const conn = navigator.connection;
          return {
            effectiveType: conn.effectiveType,
            downlink: conn.downlink,
            rtt: conn.rtt,
            saveData: conn.saveData
          };
        }
        return null;
      },
      
      // Get resource summary
      getResourceSummary: function() {
        const summary = {
          total: this.resourceMetrics.length,
          byType: {},
          totalSize: 0,
          cachedResources: 0,
          slowResources: 0
        };
        
        this.resourceMetrics.forEach(resource => {
          // Count by type
          if (!summary.byType[resource.type]) {
            summary.byType[resource.type] = { count: 0, size: 0 };
          }
          summary.byType[resource.type].count++;
          summary.byType[resource.type].size += resource.transferSize;
          
          // Total size
          summary.totalSize += resource.transferSize;
          
          // Cached resources
          if (resource.isCached) {
            summary.cachedResources++;
          }
          
          // Slow resources
          if (resource.duration > 1000) {
            summary.slowResources++;
          }
        });
        
        return summary;
      },
      
      // Get performance summary
      getPerformanceSummary: function() {
        return {
          domContentLoaded: performance.timing ? 
            performance.timing.domContentLoadedEventEnd - performance.timing.navigationStart : null,
          loadComplete: performance.timing ? 
            performance.timing.loadEventEnd - performance.timing.navigationStart : null,
          memoryUsage: performance.memory ? {
            used: performance.memory.usedJSHeapSize,
            total: performance.memory.totalJSHeapSize,
            limit: performance.memory.jsHeapSizeLimit
          } : null
        };
      },
      
      // Get current metrics for debugging
      getCurrentMetrics: function() {
        return {
          coreWebVitals: this.metrics,
          resourceCount: this.resourceMetrics.length,
          errorCount: this.errors.length,
          userTimingCount: this.userMetrics.length
        };
      }
    };
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => PerformanceMonitor.init());
    } else {
      PerformanceMonitor.init();
    }
    
    // Expose for debugging
    window.PerformanceMonitor = PerformanceMonitor;
    
    // Add performance marks for key events
    if (performance.mark) {
      performance.mark('performance-monitor-loaded');
    }
  })();
</script>

<!-- Performance monitoring styles -->
<style>
  /* Performance debugging overlay (only visible in development) */
  .perf-debug {
    position: fixed;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px;
    border-radius: 5px;
    font-family: monospace;
    font-size: 12px;
    z-index: 9999;
    display: none;
  }
  
  .perf-debug.visible {
    display: block;
  }
  
  .perf-metric {
    margin: 2px 0;
  }
  
  .perf-good { color: #4ade80; }
  .perf-needs-improvement { color: #fbbf24; }
  .perf-poor { color: #f87171; }
</style>
